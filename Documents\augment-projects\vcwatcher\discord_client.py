"""
Discord API Client for Channel Monitoring
"""
import requests
import time
import logging
from typing import Dict, List, Optional, Any
from config import Config

logger = logging.getLogger(__name__)

class DiscordAPIError(Exception):
    """Custom exception for Discord API errors"""
    def __init__(self, status_code: int, message: str, response_data: Optional[Dict] = None):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data
        super().__init__(f"Discord API Error {status_code}: {message}")

class RateLimitError(DiscordAPIError):
    """Exception for rate limit errors"""
    def __init__(self, retry_after: float, message: str = "Rate limited"):
        self.retry_after = retry_after
        super().__init__(429, message)

class DiscordClient:
    """Discord REST API Client"""
    
    def __init__(self):
        self.base_url = Config.DISCORD_API_BASE
        self.headers = Config.get_headers()
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.last_request_time = 0
        
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """Make a request to Discord API with rate limiting and error handling"""
        url = f"{self.base_url}{endpoint}"
        
        # Rate limiting - ensure minimum delay between requests
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < Config.RATE_LIMIT_DELAY:
            sleep_time = Config.RATE_LIMIT_DELAY - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        
        # Make the request with retries
        for attempt in range(Config.MAX_RETRIES):
            try:
                logger.debug(f"Making {method} request to {endpoint} (attempt {attempt + 1})")
                response = self.session.request(method, url, **kwargs)
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = response.json().get('retry_after', Config.RETRY_DELAY)
                    logger.warning(f"Rate limited. Retrying after {retry_after} seconds")
                    if attempt < Config.MAX_RETRIES - 1:
                        time.sleep(retry_after)
                        continue
                    else:
                        raise RateLimitError(retry_after, "Rate limit exceeded after max retries")
                
                # Handle other HTTP errors
                if not response.ok:
                    error_data = None
                    try:
                        error_data = response.json()
                    except:
                        pass
                    
                    error_message = f"HTTP {response.status_code}"
                    if error_data and 'message' in error_data:
                        error_message += f": {error_data['message']}"
                    
                    raise DiscordAPIError(response.status_code, error_message, error_data)
                
                # Success - return JSON data
                return response.json()
                
            except requests.RequestException as e:
                logger.error(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt < Config.MAX_RETRIES - 1:
                    time.sleep(Config.RETRY_DELAY)
                    continue
                else:
                    raise DiscordAPIError(0, f"Request failed after {Config.MAX_RETRIES} attempts: {e}")
        
        # This should never be reached
        raise DiscordAPIError(0, "Unexpected error in request handling")
    
    def get_channel_info(self, channel_id: str) -> Dict[Any, Any]:
        """Get information about a channel"""
        endpoint = f"/channels/{channel_id}"
        return self._make_request('GET', endpoint)
    
    def get_guild_info(self, guild_id: str) -> Dict[Any, Any]:
        """Get information about a guild"""
        endpoint = f"/guilds/{guild_id}"
        return self._make_request('GET', endpoint)
    
    def get_guild_members(self, guild_id: str, limit: int = 1000, after: Optional[str] = None) -> List[Dict[Any, Any]]:
        """Get guild members"""
        endpoint = f"/guilds/{guild_id}/members"
        params = {'limit': limit}
        if after:
            params['after'] = after
        
        return self._make_request('GET', endpoint, params=params)
    
    def get_channel_messages(self, channel_id: str, limit: int = 50) -> List[Dict[Any, Any]]:
        """Get recent messages from a channel (to infer active members)"""
        endpoint = f"/channels/{channel_id}/messages"
        params = {'limit': limit}
        return self._make_request('GET', endpoint, params=params)
    
    def search_guild_members(self, guild_id: str, query: str = "", limit: int = 1000) -> List[Dict[Any, Any]]:
        """Search guild members"""
        endpoint = f"/guilds/{guild_id}/members/search"
        params = {'query': query, 'limit': limit}
        return self._make_request('GET', endpoint, params=params)
    
    def get_channel_permissions(self, channel_id: str, user_id: str) -> Dict[Any, Any]:
        """Get user permissions for a specific channel"""
        # This might not be directly available, we'll need to check channel overwrites
        channel_info = self.get_channel_info(channel_id)
        return channel_info.get('permission_overwrites', [])
    
    def close(self):
        """Close the session"""
        self.session.close()
