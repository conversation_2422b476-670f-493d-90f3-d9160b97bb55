"""
Configuration settings for Discord Channel Watcher
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for Discord Channel Watcher"""
    
    # Discord API Configuration
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
    GUILD_ID = os.getenv('GUILD_ID', '1253030405343543429')
    CHANNEL_ID = os.getenv('CHANNEL_ID', '1253036826004951080')
    
    # API Configuration
    DISCORD_API_BASE = 'https://discord.com/api/v9'
    USER_AGENT = os.getenv('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    X_SUPER_PROPERTIES = os.getenv('X_SUPER_PROPERTIES', 'eyJvcyI6IldpbmRvd3MiLCJicm93c2VyIjoiQ2hyb21lIiwiZGV2aWNlIjoiIiwic3lzdGVtX2xvY2FsZSI6ImVuLVVTIiwiYnJvd3Nlcl91c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEyMC4wLjAuMCBTYWZhcmkvNTM3LjM2IiwiYnJvd3Nlcl92ZXJzaW9uIjoiMTIwLjAuMC4wIiwib3NfdmVyc2lvbiI6IjEwIiwicmVmZXJyZXIiOiIiLCJyZWZlcnJpbmdfZG9tYWluIjoiIiwicmVmZXJyZXJfY3VycmVudCI6IiIsInJlZmVycmluZ19kb21haW5fY3VycmVudCI6IiIsInJlbGVhc2VfY2hhbm5lbCI6InN0YWJsZSIsImNsaWVudF9idWlsZF9udW1iZXIiOjI2MzQ5MywiY2xpZW50X2V2ZW50X3NvdXJjZSI6bnVsbH0=')
    
    # Monitoring Configuration
    CHECK_INTERVAL = int(os.getenv('CHECK_INTERVAL', '300'))  # 5 minutes default
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    RETRY_DELAY = int(os.getenv('RETRY_DELAY', '5'))
    
    # Rate Limiting
    RATE_LIMIT_DELAY = float(os.getenv('RATE_LIMIT_DELAY', '1.0'))  # Delay between requests
    
    # Storage Configuration
    DATA_DIR = os.getenv('DATA_DIR', 'data')
    LOG_FILE = os.getenv('LOG_FILE', 'vcwatcher.log')
    MEMBERS_FILE = os.getenv('MEMBERS_FILE', 'members_history.json')
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    @classmethod
    def validate(cls):
        """Validate required configuration"""
        if not cls.DISCORD_TOKEN:
            raise ValueError("DISCORD_TOKEN is required. Please set it in your .env file.")
        
        if not cls.GUILD_ID:
            raise ValueError("GUILD_ID is required.")
            
        if not cls.CHANNEL_ID:
            raise ValueError("CHANNEL_ID is required.")
    
    @classmethod
    def get_headers(cls):
        """Get HTTP headers for Discord API requests"""
        return {
            'Authorization': cls.DISCORD_TOKEN,
            'User-Agent': cls.USER_AGENT,
            'X-Super-Properties': cls.X_SUPER_PROPERTIES,
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
