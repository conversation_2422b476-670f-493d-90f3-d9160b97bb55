"""
Logging setup for Discord Channel Watcher
"""
import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
from config import Config

def setup_logging():
    """Setup logging configuration"""
    
    # Create data directory if it doesn't exist
    data_dir = Path(Config.DATA_DIR)
    data_dir.mkdir(exist_ok=True)
    
    # Create log file path
    log_file = data_dir / Config.LOG_FILE
    
    # Create formatter
    formatter = logging.Formatter(Config.LOG_FORMAT)
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Console handler with colors
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
    
    # Add color formatting for console
    try:
        import colorama
        colorama.init()
        
        class ColoredFormatter(logging.Formatter):
            """Colored log formatter"""
            
            COLORS = {
                'DEBUG': '\033[36m',    # Cyan
                'INFO': '\033[32m',     # Green
                'WARNING': '\033[33m',  # Yellow
                'ERROR': '\033[31m',    # Red
                'CRITICAL': '\033[35m', # Magenta
            }
            RESET = '\033[0m'
            
            def format(self, record):
                log_color = self.COLORS.get(record.levelname, '')
                record.levelname = f"{log_color}{record.levelname}{self.RESET}"
                return super().format(record)
        
        console_handler.setFormatter(ColoredFormatter(Config.LOG_FORMAT))
    except ImportError:
        # Fallback to regular formatter if colorama not available
        console_handler.setFormatter(formatter)
    
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)  # Always log everything to file
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Create a separate handler for membership changes
    changes_log_file = data_dir / 'membership_changes.log'
    changes_handler = logging.handlers.RotatingFileHandler(
        changes_log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=10,
        encoding='utf-8'
    )
    changes_handler.setLevel(logging.INFO)
    
    # Custom formatter for changes log
    changes_formatter = logging.Formatter(
        '%(asctime)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    changes_handler.setFormatter(changes_formatter)
    
    # Create a logger specifically for membership changes
    changes_logger = logging.getLogger('membership_changes')
    changes_logger.setLevel(logging.INFO)
    changes_logger.addHandler(changes_handler)
    changes_logger.propagate = False  # Don't propagate to root logger
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info("="*50)
    logger.info("Discord Channel Watcher Started")
    logger.info(f"Log Level: {Config.LOG_LEVEL}")
    logger.info(f"Log File: {log_file}")
    logger.info(f"Changes Log: {changes_log_file}")
    logger.info("="*50)

def log_membership_change(change_type: str, member_info: dict, channel_id: str, guild_id: str):
    """Log a membership change to the dedicated changes log"""
    changes_logger = logging.getLogger('membership_changes')
    
    username = member_info.get('username', 'Unknown')
    discriminator = member_info.get('discriminator', '0000')
    user_id = member_info.get('user_id', 'Unknown')
    display_name = member_info.get('display_name', '')
    
    display_part = f" ({display_name})" if display_name and display_name != username else ""
    
    message = (
        f"[{change_type.upper()}] "
        f"{username}#{discriminator}{display_part} "
        f"(ID: {user_id}) "
        f"- Channel: {channel_id} "
        f"- Guild: {guild_id}"
    )
    
    changes_logger.info(message)

def log_monitoring_summary(total_members: int, changes_count: int, channel_id: str):
    """Log a monitoring summary"""
    logger = logging.getLogger('vcwatcher')
    
    if changes_count > 0:
        logger.info(f"Monitoring complete: {total_members} total members, {changes_count} changes detected")
    else:
        logger.debug(f"Monitoring complete: {total_members} total members, no changes")

class PerformanceTimer:
    """Context manager for timing operations"""
    
    def __init__(self, operation_name: str, logger_name: str = __name__):
        self.operation_name = operation_name
        self.logger = logging.getLogger(logger_name)
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.debug(f"Starting {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            if exc_type:
                self.logger.error(f"{self.operation_name} failed after {duration:.2f}s")
            else:
                self.logger.debug(f"{self.operation_name} completed in {duration:.2f}s")

# Convenience function to get a logger with the app name
def get_logger(name: str = None) -> logging.Logger:
    """Get a logger with the application prefix"""
    if name:
        return logging.getLogger(f"vcwatcher.{name}")
    return logging.getLogger("vcwatcher")
