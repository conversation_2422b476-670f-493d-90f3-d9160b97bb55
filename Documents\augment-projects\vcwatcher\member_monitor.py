"""
Discord Channel Member Monitoring Logic
"""
import json
import logging
from datetime import datetime
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

from discord_client import DiscordClient, DiscordAPIError
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class MemberInfo:
    """Information about a Discord member"""
    user_id: str
    username: str
    display_name: Optional[str]
    discriminator: str
    avatar: Optional[str]
    joined_at: Optional[str]
    roles: List[str]
    
    def __hash__(self):
        return hash(self.user_id)
    
    def __eq__(self, other):
        if not isinstance(other, MemberInfo):
            return False
        return self.user_id == other.user_id

@dataclass
class MembershipSnapshot:
    """Snapshot of channel membership at a point in time"""
    timestamp: str
    channel_id: str
    guild_id: str
    members: List[MemberInfo]
    member_count: int
    
    def get_member_ids(self) -> Set[str]:
        """Get set of member IDs"""
        return {member.user_id for member in self.members}

@dataclass
class MembershipChange:
    """Represents a change in channel membership"""
    timestamp: str
    change_type: str  # 'joined' or 'left'
    member: MemberInfo
    channel_id: str
    guild_id: str

class ChannelMemberMonitor:
    """Monitors Discord channel membership changes"""
    
    def __init__(self, discord_client: DiscordClient):
        self.client = discord_client
        self.data_dir = Path(Config.DATA_DIR)
        self.data_dir.mkdir(exist_ok=True)
        
        self.members_file = self.data_dir / Config.MEMBERS_FILE
        self.last_snapshot: Optional[MembershipSnapshot] = None
        
        # Load previous snapshot if exists
        self._load_last_snapshot()
    
    def _load_last_snapshot(self):
        """Load the last membership snapshot from file"""
        if self.members_file.exists():
            try:
                with open(self.members_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if data and 'snapshots' in data and data['snapshots']:
                        last_snapshot_data = data['snapshots'][-1]
                        members = [MemberInfo(**member) for member in last_snapshot_data['members']]
                        self.last_snapshot = MembershipSnapshot(
                            timestamp=last_snapshot_data['timestamp'],
                            channel_id=last_snapshot_data['channel_id'],
                            guild_id=last_snapshot_data['guild_id'],
                            members=members,
                            member_count=last_snapshot_data['member_count']
                        )
                        logger.info(f"Loaded last snapshot from {self.last_snapshot.timestamp} with {self.last_snapshot.member_count} members")
            except Exception as e:
                logger.error(f"Failed to load last snapshot: {e}")
                self.last_snapshot = None
    
    def _save_snapshot(self, snapshot: MembershipSnapshot):
        """Save a membership snapshot to file"""
        try:
            # Load existing data or create new structure
            data = {'snapshots': [], 'changes': []}
            if self.members_file.exists():
                with open(self.members_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # Add new snapshot
            snapshot_dict = asdict(snapshot)
            data['snapshots'].append(snapshot_dict)
            
            # Keep only last 100 snapshots to prevent file from growing too large
            if len(data['snapshots']) > 100:
                data['snapshots'] = data['snapshots'][-100:]
            
            # Save to file
            with open(self.members_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.debug(f"Saved snapshot with {snapshot.member_count} members")
            
        except Exception as e:
            logger.error(f"Failed to save snapshot: {e}")
    
    def _save_changes(self, changes: List[MembershipChange]):
        """Save membership changes to file"""
        if not changes:
            return
            
        try:
            # Load existing data
            data = {'snapshots': [], 'changes': []}
            if self.members_file.exists():
                with open(self.members_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # Add new changes
            for change in changes:
                change_dict = asdict(change)
                data['changes'].append(change_dict)
            
            # Keep only last 1000 changes
            if len(data['changes']) > 1000:
                data['changes'] = data['changes'][-1000:]
            
            # Save to file
            with open(self.members_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(changes)} membership changes")
            
        except Exception as e:
            logger.error(f"Failed to save changes: {e}")
    
    def _extract_member_info(self, member_data: Dict[Any, Any]) -> MemberInfo:
        """Extract member information from Discord API response"""
        user = member_data.get('user', {})
        return MemberInfo(
            user_id=user.get('id', ''),
            username=user.get('username', ''),
            display_name=member_data.get('nick') or user.get('global_name'),
            discriminator=user.get('discriminator', '0'),
            avatar=user.get('avatar'),
            joined_at=member_data.get('joined_at'),
            roles=member_data.get('roles', [])
        )
    
    def _get_channel_members_from_guild(self, guild_id: str, channel_id: str) -> List[MemberInfo]:
        """
        Get channel members by checking guild members and their permissions
        This is a workaround since Discord doesn't provide direct channel member endpoints
        """
        try:
            # Get channel info to understand permissions
            channel_info = self.client.get_channel_info(channel_id)
            logger.debug(f"Channel info: {channel_info.get('name', 'Unknown')} (Type: {channel_info.get('type', 'Unknown')})")
            
            # Get all guild members (this might be limited by permissions)
            all_members = []
            after = None
            
            while True:
                try:
                    members_batch = self.client.get_guild_members(guild_id, limit=1000, after=after)
                    if not members_batch:
                        break
                    
                    all_members.extend(members_batch)
                    
                    if len(members_batch) < 1000:
                        break
                    
                    # Set after to the last member's ID for pagination
                    after = members_batch[-1]['user']['id']
                    
                except DiscordAPIError as e:
                    if e.status_code == 403:
                        logger.warning("Insufficient permissions to fetch all guild members")
                        break
                    raise
            
            logger.info(f"Retrieved {len(all_members)} total guild members")
            
            # Convert to MemberInfo objects
            members = [self._extract_member_info(member) for member in all_members]
            
            # For private channels, we might need to filter based on roles or other criteria
            # This is a simplified approach - in reality, you'd need to check channel permissions
            return members
            
        except DiscordAPIError as e:
            logger.error(f"Failed to get channel members: {e}")
            raise
    
    def get_current_members(self, guild_id: str, channel_id: str) -> MembershipSnapshot:
        """Get current channel membership"""
        try:
            members = self._get_channel_members_from_guild(guild_id, channel_id)
            
            snapshot = MembershipSnapshot(
                timestamp=datetime.now().isoformat(),
                channel_id=channel_id,
                guild_id=guild_id,
                members=members,
                member_count=len(members)
            )
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Failed to get current members: {e}")
            raise
    
    def detect_changes(self, current_snapshot: MembershipSnapshot) -> List[MembershipChange]:
        """Detect changes between current and last snapshot"""
        if not self.last_snapshot:
            logger.info("No previous snapshot found - treating all members as new")
            return []
        
        current_ids = current_snapshot.get_member_ids()
        last_ids = self.last_snapshot.get_member_ids()
        
        changes = []
        timestamp = current_snapshot.timestamp
        
        # Find members who joined
        joined_ids = current_ids - last_ids
        for member in current_snapshot.members:
            if member.user_id in joined_ids:
                changes.append(MembershipChange(
                    timestamp=timestamp,
                    change_type='joined',
                    member=member,
                    channel_id=current_snapshot.channel_id,
                    guild_id=current_snapshot.guild_id
                ))
        
        # Find members who left
        left_ids = last_ids - current_ids
        for member in self.last_snapshot.members:
            if member.user_id in left_ids:
                changes.append(MembershipChange(
                    timestamp=timestamp,
                    change_type='left',
                    member=member,
                    channel_id=current_snapshot.channel_id,
                    guild_id=current_snapshot.guild_id
                ))
        
        return changes
    
    def monitor_once(self, guild_id: str, channel_id: str) -> List[MembershipChange]:
        """Perform one monitoring check"""
        logger.info(f"Checking membership for channel {channel_id} in guild {guild_id}")
        
        try:
            # Get current membership
            current_snapshot = self.get_current_members(guild_id, channel_id)
            
            # Detect changes
            changes = self.detect_changes(current_snapshot)
            
            # Log changes
            if changes:
                logger.info(f"Detected {len(changes)} membership changes")
                for change in changes:
                    logger.info(f"  {change.change_type.upper()}: {change.member.username}#{change.member.discriminator} ({change.member.user_id})")
            else:
                logger.info("No membership changes detected")
            
            # Save snapshot and changes
            self._save_snapshot(current_snapshot)
            if changes:
                self._save_changes(changes)
            
            # Update last snapshot
            self.last_snapshot = current_snapshot
            
            return changes
            
        except Exception as e:
            logger.error(f"Error during monitoring: {e}")
            raise
