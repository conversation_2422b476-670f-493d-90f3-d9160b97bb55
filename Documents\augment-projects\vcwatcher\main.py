#!/usr/bin/env python3
"""
Discord Channel Watcher - Main Application
Monitors Discord channel membership using REST API
"""
import signal
import sys
import time
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from config import Config
from logger_setup import setup_logging, get_logger, log_membership_change, log_monitoring_summary, PerformanceTimer
from discord_client import DiscordClient, DiscordAPIError
from member_monitor import ChannelMemberMonitor, MembershipChange
from error_handler import retry_on_error, error_handler, safe_execute, CircuitBreaker

# Global flag for graceful shutdown
shutdown_requested = False
monitor_thread: Optional[threading.Thread] = None

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    global shutdown_requested
    logger = get_logger('main')
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_requested = True

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

class DiscordChannelWatcher:
    """Main application class"""
    
    def __init__(self):
        self.logger = get_logger('watcher')
        self.discord_client: Optional[DiscordClient] = None
        self.member_monitor: Optional[ChannelMemberMonitor] = None
        self.circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=300)  # 5 minutes
        self.stats = {
            'start_time': datetime.now(),
            'total_checks': 0,
            'total_changes': 0,
            'last_check': None,
            'last_change': None
        }
    
    def initialize(self):
        """Initialize the application components"""
        self.logger.info("Initializing Discord Channel Watcher...")
        
        try:
            # Validate configuration
            Config.validate()
            self.logger.info("Configuration validated successfully")
            
            # Initialize Discord client
            self.discord_client = DiscordClient()
            self.logger.info("Discord client initialized")
            
            # Initialize member monitor
            self.member_monitor = ChannelMemberMonitor(self.discord_client)
            self.logger.info("Member monitor initialized")
            
            # Test connection
            self._test_connection()
            
            self.logger.info("Initialization completed successfully")
            
        except Exception as e:
            self.logger.critical(f"Failed to initialize application: {e}")
            raise
    
    def _test_connection(self):
        """Test Discord API connection"""
        self.logger.info("Testing Discord API connection...")
        
        try:
            # Test guild access
            guild_info = self.discord_client.get_guild_info(Config.GUILD_ID)
            self.logger.info(f"Connected to guild: {guild_info.get('name', 'Unknown')} (ID: {Config.GUILD_ID})")
            
            # Test channel access
            channel_info = self.discord_client.get_channel_info(Config.CHANNEL_ID)
            self.logger.info(f"Monitoring channel: {channel_info.get('name', 'Unknown')} (ID: {Config.CHANNEL_ID})")
            
        except DiscordAPIError as e:
            if e.status_code == 403:
                self.logger.error("Permission denied - check bot permissions for guild and channel")
            elif e.status_code == 404:
                self.logger.error("Guild or channel not found - check IDs in configuration")
            else:
                self.logger.error(f"Discord API error during connection test: {e}")
            raise
    
    @retry_on_error(max_retries=2)
    def _perform_monitoring_check(self) -> int:
        """Perform a single monitoring check"""
        with PerformanceTimer("Monitoring check", "watcher"):
            changes = self.member_monitor.monitor_once(Config.GUILD_ID, Config.CHANNEL_ID)
            
            # Log changes to dedicated log
            for change in changes:
                log_membership_change(
                    change.change_type,
                    {
                        'username': change.member.username,
                        'discriminator': change.member.discriminator,
                        'user_id': change.member.user_id,
                        'display_name': change.member.display_name
                    },
                    change.channel_id,
                    change.guild_id
                )
            
            # Update stats
            self.stats['total_checks'] += 1
            self.stats['last_check'] = datetime.now()
            
            if changes:
                self.stats['total_changes'] += len(changes)
                self.stats['last_change'] = datetime.now()
            
            # Log summary
            current_member_count = self.member_monitor.last_snapshot.member_count if self.member_monitor.last_snapshot else 0
            log_monitoring_summary(current_member_count, len(changes), Config.CHANNEL_ID)
            
            return len(changes)
    
    def run_monitoring_loop(self):
        """Run the main monitoring loop"""
        self.logger.info(f"Starting monitoring loop (check interval: {Config.CHECK_INTERVAL}s)")
        
        while not shutdown_requested:
            try:
                # Use circuit breaker to prevent cascading failures
                changes_count = self.circuit_breaker.call(self._perform_monitoring_check)
                
                if changes_count > 0:
                    self.logger.info(f"Detected {changes_count} membership changes")
                
            except Exception as e:
                should_continue = error_handler.handle_error(e, "monitoring_loop")
                if not should_continue:
                    self.logger.critical("Critical error in monitoring loop, shutting down")
                    break
                
                # Wait a bit longer after errors
                error_delay = min(Config.CHECK_INTERVAL * 2, 300)  # Max 5 minutes
                self.logger.info(f"Waiting {error_delay}s before next check due to error")
                
                for _ in range(error_delay):
                    if shutdown_requested:
                        break
                    time.sleep(1)
                continue
            
            # Wait for next check
            self.logger.debug(f"Waiting {Config.CHECK_INTERVAL}s until next check...")
            for _ in range(Config.CHECK_INTERVAL):
                if shutdown_requested:
                    break
                time.sleep(1)
        
        self.logger.info("Monitoring loop stopped")
    
    def print_stats(self):
        """Print application statistics"""
        uptime = datetime.now() - self.stats['start_time']
        
        print("\n" + "="*50)
        print("Discord Channel Watcher Statistics")
        print("="*50)
        print(f"Uptime: {uptime}")
        print(f"Total checks: {self.stats['total_checks']}")
        print(f"Total changes detected: {self.stats['total_changes']}")
        print(f"Last check: {self.stats['last_check']}")
        print(f"Last change: {self.stats['last_change']}")
        
        if self.member_monitor and self.member_monitor.last_snapshot:
            print(f"Current members: {self.member_monitor.last_snapshot.member_count}")
        
        # Error summary
        error_summary = error_handler.get_error_summary()
        if error_summary['error_counts']:
            print("\nError Summary:")
            for error_type, count in error_summary['error_counts'].items():
                print(f"  {error_type}: {count}")
        
        print("="*50)
    
    def cleanup(self):
        """Cleanup resources"""
        self.logger.info("Cleaning up resources...")
        
        if self.discord_client:
            self.discord_client.close()
        
        self.print_stats()
        self.logger.info("Cleanup completed")

def main():
    """Main entry point"""
    global monitor_thread
    
    # Setup logging
    setup_logging()
    logger = get_logger('main')
    
    logger.info("Starting Discord Channel Watcher")
    
    # Setup signal handlers
    setup_signal_handlers()
    
    watcher = None
    try:
        # Initialize watcher
        watcher = DiscordChannelWatcher()
        watcher.initialize()
        
        # Start monitoring in a separate thread
        monitor_thread = threading.Thread(target=watcher.run_monitoring_loop, daemon=True)
        monitor_thread.start()
        
        logger.info("Monitoring started. Press Ctrl+C to stop.")
        
        # Keep main thread alive
        while not shutdown_requested:
            time.sleep(1)
        
        # Wait for monitoring thread to finish
        if monitor_thread and monitor_thread.is_alive():
            logger.info("Waiting for monitoring thread to finish...")
            monitor_thread.join(timeout=10)
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.critical(f"Critical error: {e}")
        return 1
    finally:
        if watcher:
            watcher.cleanup()
    
    logger.info("Discord Channel Watcher stopped")
    return 0

if __name__ == "__main__":
    sys.exit(main())
